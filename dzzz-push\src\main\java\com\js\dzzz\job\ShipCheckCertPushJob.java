package com.js.dzzz.job;

import com.js.dzzz.enums.CertPushTaskEnum;
import com.js.dzzz.enums.CertificateTypeCode;
import com.js.dzzz.service.CertificatePushService;
import com.js.dzzz.vo.CertPushRequestVO;
import com.js.dzzz.vo.CertificatePushVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 船舶检验证书推送任务（油污残骸）
 */
@Slf4j
@Component
public class ShipCheckCertPushJob extends PushJob{

    @Resource
    private CertificatePushService certificatePushService;
    // 油污残骸类型代码
    private static final List<String> CERTIFICATE_NAME = Arrays.asList(
            CertificateTypeCode.OIL_DAMAGE.getName(),
            CertificateTypeCode.FUEL_POLLUTION.getName(),
            CertificateTypeCode.NON_PERSISTENT.getName(),
            CertificateTypeCode.DEBRIS.getName());

    @Value("${job.shipCheckCertPushJob.enabled}")
    private boolean enable;

    @Value("${job.shipCheckCertPushJob.pushUrl}")
    private String pushUrl;

    @Value("${job.shipCheckCertPushJob.authUser}")
    private String authUser;

    @Value("${job.shipCheckCertPushJob.authPassword}")
    private String authPassword;

    @Value("${job.shipCheckCertPushJob.batchSize:100}")
    private int batchSize;

    @Scheduled(cron = "${job.shipCheckCertPushJob.cron}")
    @Transactional(rollbackFor = Exception.class)
    public void execute() {
        if (!enable) {
            return;
        }

        String name = CertPushTaskEnum.SHIP_CHECK_CERT.getName();
        Date start = new Date();
        log.info("开始执行{}定时任务:{}", name, start);

        try {
            this.push(name);
        } catch (Exception e) {
            log.error("{}执行异常", name, e);
        }
        Date end = new Date();
        log.info("{}定时任务结束:{}，耗时:{}", name, end, end.getTime() - start.getTime());
    }

    @Override
    public void push(String name) {
        // 封装请求参数
        CertPushRequestVO requestParams = CertPushRequestVO.builder()
                .name(name)
                .url(pushUrl)
                .authUser(authUser)
                .authPassword(authPassword)
                .build();

        // 查询需要推送的证照数据
        List<CertificatePushVO> pushVOList = certificatePushService
                .queryCertificateDataByType(name, CERTIFICATE_NAME, batchSize);
        if (pushVOList.isEmpty()) {
            log.info("{}没有需要推送的数据", name);
            return;
        }

        log.info("{}查询到{}条需要推送的数据", name, pushVOList.size());

        // 先重试推送失败的数据，重试间隔5分钟
        certificatePushService.retryFailedPushData(name, requestParams, 5, CERTIFICATE_NAME);

        // 推送数据
        boolean success = certificatePushService.pushCertificateData(pushVOList, requestParams);
        log.info("{}推送完成，结果：{}", name, success ? "成功" : "失败");
    }
}
