package com.js.dzzz.vo;

import lombok.Data;

/**
 * 证照目录VO
 */
@Data
public class SrCertCatalogVO {
    /**
     * 目录主键
     */
    private String catalogId;

    /**
     * 目录名称
     */
    private String catalogName;

    /**
     * 证照类型名称
     */
    private String certificateType;

    /**
     * 证照类型代码
     */
    private String certificateTypeCode;

    /**
     * 证照定义机构
     */
    private String certificateDefineDept;

    /**
     * 证照定义机构代码
     */
    private String certificateDefineDeptCode;

    /**
     * 关联事项名称
     */
    private String relatedItemName;

    /**
     * 关联事项代码
     */
    private String relatedItemCode;

    /**
     * 持证主体类别
     */
    private String holderType;

    /**
     * 有效期限范围
     */
    private String certificateLife;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 修改时间
     */
    private String updateTime;

    /**
     * 修改人员ID
     */
    private String operatorId;

    /**
     * 目录类型状态 0-草稿，1-审核通过，2-待审核，3-取消
     */
    private String status;

    /**
     * 区域编码
     */
    private String areaCode;

    /**
     * 备注
     */
    private String remarks;
}