package com.js.dzzz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.js.dzzz.entity.DwdbCertificateData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Map;
import java.util.List;

/**
 * 证照数据Mapper接口
 */
@Mapper
public interface DwdbCertificateDataMapper extends BaseMapper<DwdbCertificateData> {

    /**
     * 根据证照类型代码查询证照数据和目录数据
     *
     * @param certificateTypeName 证照类型名称
     * @return 证照数据和目录数据Map
     */
    List<Map<String, Object>> selectCertificateDataByType(
            @Param("certificateTypeName") List<String> certificateTypeName, @Param("taskName") String taskName, @Param("pushCount") int pushCount);


    /**
     * 根据dataId列表批量查询证照数据和目录数据
     * 
     * @param dataIds             dataId列表
     * @param taskName            任务名称
     * @param certificateTypeName 证照类型名称
     * @return 证照数据和目录数据Map列表
     */
    List<Map<String, Object>> selectCertificateDataByDataIds(@Param("dataIds") List<String> dataIds,
            @Param("taskName") String taskName, @Param("certificateTypeName") List<String> certificateTypeName);
}
