spring:
  datasource:
    # 标准库数据源配置
    standard:
      jdbc-url: ********************************************************************************************************
      username: system
      password: system
      driver-class-name: org.postgresql.Driver
      # 连接池优化配置
      maximum-pool-size: 10               # 连接池最大连接数
      minimum-idle: 5                     # 连接池最小空闲连接数
      idle-timeout: 60000                 # 空闲连接超时时间(毫秒)
      connection-timeout: 30000           # 连接超时时间(毫秒)
      max-lifetime: 1800000               # 连接最大生命周期(毫秒，30分钟)
      auto-commit: true                   # 自动提交
      pool-name: StandardHikariCP         # 连接池名称
      validation-timeout: 5000            # 验证连接的最大等待时间(毫秒)
      connection-test-query: SELECT 1     # 连接测试查询
      leak-detection-threshold: 60000     # 连接泄露检测阈值(毫秒)

# Mybatis-plus
mybatis-plus:
  # 放在resource目录 classpath:/mapper/*Mapper.xml
  #mapper-locations: classpath:/mapper/*Mapper.xml
  mapper-locations: classpath*:mapper/**/**.xml
  global-config:
    # 主键类型  0:"数据库ID自增", 1:"用户输入ID",2:"全局唯一ID (数字类型唯一ID)", 3:"全局唯一ID UUID";
    id-type: 2
    # 字段策略 0:"忽略判断",1:"非 NULL 判断",2:"非空判断"
    field-strategy: 2
    # 驼峰下划线转换
    db-column-underline: true
    # 刷新mapper 调试神器
    refresh-mapper: true
    # SQL 解析缓存，开启后多租户 @SqlParser 注解生效
    sql-parser-cache: true
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    # 配置JdbcTypeForNull, oracle数据库必须配置
    jdbc-type-for-null: 'null'
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

logging:
  level:
    com.js.dzzz: debug

job:
  shipCertPushJob:
    # 是否启用
    enabled: false
    # 推送地址
    pushUrl: http://***********:28080/v1/service/cert/sr/demo
    # 认证用户
    authUser: tcauthsr
    # 认证密码
    authPassword: sr0041
    # 定时任务执行时间
    cron: "0/10 * * * * ?"
    # 单批推送数量
    batchSize: 1
  shipCheckCertPushJob:
    # 是否启用
    enabled: false
    # 推送地址
    pushUrl: http://***********:28080/v1/service/cert/sv/demo
    # 认证用户
    authUser: tcauthsv
    # 认证密码
    authPassword: sv0041
    # 定时任务执行时间
    cron: "0/10 * * * * ?"
    # 单批推送数量
    batchSize: 1
  shipCrewCertPushJob:
    # 是否启用
    enabled: false
    # 推送地址
    pushUrl: http://***********:28080/v1/service/cert/crw/demo
    # 认证用户
    authUser: tcauthcrw
    # 认证密码
    authPassword: crw0041
    # 定时任务执行时间  
    cron: "0/10 * * * * ?"
    # 单批推送数量
    batchSize: 1
