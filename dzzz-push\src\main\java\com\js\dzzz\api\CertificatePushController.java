package com.js.dzzz.api;

import com.js.dzzz.enums.CertPushTaskEnum;
import com.js.dzzz.job.PushJob;
import com.js.dzzz.job.ShipCertPushJob;
import com.js.dzzz.job.ShipCheckCertPushJob;
import com.js.dzzz.job.ShipCrewCertPushJob;
import com.js.dzzz.service.CertificatePushService;
import com.js.dzzz.vo.CertPushRequestVO;
import com.js.dzzz.vo.CertificatePushVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * 证照推送测试接口
 */
@Slf4j
@RestController
@RequestMapping("/api/certificate/push")
public class CertificatePushController {

    @Resource
    private CertificatePushService certificatePushService;

    @Resource
    private ShipCertPushJob shipCertPushJob;

    @Resource
    private ShipCheckCertPushJob shipCheckCertPushJob;

    @Resource
    private ShipCrewCertPushJob shipCrewCertPushJob;

    /**
     * 手动推送证照数据测试接口
     *
     * @param certPushRequestVO
     * @return 推送结果
     */
    @PostMapping("/test")
    public boolean testPush(@RequestBody List<CertificatePushVO> certPushRequestVO) {
        log.info("收到测试推送请求，参数：{}", certPushRequestVO);
        CertPushRequestVO requestParams = CertPushRequestVO.builder()
                .name("船舶登记系统电子证照接口")
                .url("http://***********:28080/v1/service/cert/sr/demo")
                .authUser("tcauthsr")
                .authPassword("sr0041")
                .build();
        return certificatePushService.pushCertificateData(certPushRequestVO, requestParams);
    }

    @GetMapping("/test/{taskName}")
    public void testPushByName(@PathVariable String taskName) {
        PushJob pushJob;
        switch (taskName) {
            case "shipCert":
                pushJob = shipCertPushJob;
                break;
            case "shipCheckCert":
                pushJob = shipCheckCertPushJob;
                break;
            case "shipCrewCert":
                pushJob = shipCrewCertPushJob;
                break;
            default:
                log.error("任务名称{}不存在", taskName);
                return;
        }
        CertPushTaskEnum certPushTaskEnum = CertPushTaskEnum.getCertPushTaskByCode(taskName);
        String name = Optional.ofNullable(certPushTaskEnum)
                .map(CertPushTaskEnum::getName)
                .orElse("");
        pushJob.push(name);
    }
}