package com.js.dzzz.constant;

import cn.hutool.core.map.MapUtil;

import java.util.HashMap;
import java.util.Map;

public class CertificateTypeMapping {

    public static final Map<String, String> CERTIFICATE_TYPE_TO_CODE;

    static {
        // 证照类型编码 映射
        CERTIFICATE_TYPE_TO_CODE = MapUtil.builder(new HashMap<String, String>())
                .put("海船船员培训合格证", "")
                .put("海船船员适任证书", "11100000000019713D094")
                .put("公务船船员适任证书", "")
                .put("海上非自航船舶船员适任证书", "")
                .put("小型海船适任证书", "")
                .put("船上厨师培训合格证明", "")
                .put("船上膳食服务辅助人员培训证明", "")
                .put("海船船员健康证明", "")
                .put("内河船舶船员适任证书", "11100000000019713D081")
                .put("内河船舶船员培训合格证", "")
                .put("游艇驾驶证", "")
                .put("海船船员内河航线行驶资格证明", "")
                .put("特定航线江海直达船舶船员行驶资格证明培训合格证", "")
                .put("海上设施工作人员海上交通安全技能培训合格证明", "")
                .put("海船船员适任证书承认签证", "")
                .put("船舶国籍证书", "11100000000019713D011")
                .put("船员培训质量管理体系证书", "")
                .put("海员外派机构资质证书", "11100000000019713D095")
                .put("船员培训许可证", "11100000000019713D096")
                .put("油污损害民事责任保险或其他财务保证证书", "11100000000019713D100")
                .put("燃油污染损害民事责任保险或其他财务保证证书", "11100000000019713D101")
                .put("非持久性油类污染损害民事责任保险或其他财务保证证书", "11100000000019713D102")
                .put("残骸清除责任保险或其他财务保证证书", "")
                .build();
    }
}
