<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.js.dzzz.mapper.DwdbCertificateDataMapper">

    <!-- 根据证照类型代码查询证照数据和目录数据 -->
    <select id="selectCertificateDataByType" resultType="java.util.Map">
        SELECT
            -- 证照数据字段
            dcd.data_id as data_id,
            <!-- 电子证照标识 -->
            dcd.certificate_identifier as certificate_id,
            dcd.template_id as template_id,
            dcd.certificate_type_name as certificate_type,
            dcd.certificate_type_code as certificate_type_code,
            dcd.certificate_issuing_authority_name as issue_dept,
            dcd.certificate_issuing_authority_code as issue_dept_code,
            dcd.certificate_area_code as certificate_area_code,
            dcd.certificate_holder_name as certificate_holder,
            dcd.certificate_holder_code as certificate_holder_code,
            dcd.certificate_holder_type_name as certificate_holder_type,
            dcd.certificate_number as certificate_number,
            dcd.certificate_issued_date as issue_date,
            dcd.certificate_effective_date as valid_begin_date,
            dcd.certificate_expiring_date as valid_end_date,
            dcd.surface_data as surface_data,
            dcd.certificate_status as status,
            dcd.creator_id as creator,
            dcd.create_time as create_time,
            dcd.operator_id as operator,
            dcd.update_time as update_time,
            dcd.file_path as file_path,
            dcd.sync_status as sync_status,
            dcd.remarks as remarks,
            dcd.dept_id as dept_id,

            -- 证照目录字段
            dcctd.CERT_TYPE_DIR_ID as catalog_id,
            dcctd.CERTIFICATE_TYPE_NAME as catalog_name,
            dcctd.CERTIFICATE_TYPE_NAME as certificate_type,
            dcctd.CERTIFICATE_TYPE_CODE as certificate_type_code,
            dcctd.CERTIFICATE_DEFINE_AUTHORITY_NAME as certificate_define_dept,
            dcctd.CERTIFICATE_DEFINE_AUTHORITY_CODE as certificate_define_dept_code,
            dcctd.RELATED_ITEM_NAME as related_item_name,
            dcctd.RELATED_ITEM_CODE as related_item_code,
            dcctd.CERTIFICATE_HOLDER_CATEGORY as holder_type,
            dcctd.VALIDITY_RANGE as certificate_life,
            dcctd.CREATE_TIME as dir_create_time,
            dcctd.UPDATE_TIME as dir_update_time,
            dcctd.UPDATE_BY as operator_id,
            dcctd.APPROVAL_STATUS as status,
            dcctd.CREATE_ORG_CODE as area_code
        FROM dwdb_certificate_data dcd
        INNER JOIN dwdb_ctf_cert_type_directory dcctd
            ON dcd.certificate_type_code = dcctd.CERTIFICATE_TYPE_CODE
            <choose>
                <when test='taskName == "海事船员管理系统电子证照接口"'>
                    AND dcctd.parent_id = '3'
                    <!-- 引航员船员适任证书不需要推送 -->
                    AND dcd.certificate_name != '引航员船员适任证书'
                </when>
                <otherwise>
                    AND dcd.certificate_type_name IN
                    <foreach item="code" collection="certificateTypeName" open="(" separator="," close=")">
                        #{code}
                    </foreach>
                </otherwise>
            </choose>
            AND dcd.data_id not in (SELECT data_id FROM dwdb_ctf_cert_national_push where push_status = '1')
        order by dcd.rec_create_date
        limit #{pushCount}
    </select>

    <select id="selectCertificateDataByDataIds" resultType="java.util.Map">
        SELECT
            -- 证照数据字段
            dcd.data_id as data_id,
            <!-- 电子证照标识 -->
            dcd.certificate_identifier as certificate_id,
            dcd.template_id as template_id,
            dcd.certificate_type_name as certificate_type,
            dcd.certificate_type_code as certificate_type_code,
            dcd.certificate_issuing_authority_name as issue_dept,
            dcd.certificate_issuing_authority_code as issue_dept_code,
            dcd.certificate_area_code as certificate_area_code,
            dcd.certificate_holder_name as certificate_holder,
            dcd.certificate_holder_code as certificate_holder_code,
            dcd.certificate_holder_type_name as certificate_holder_type,
            dcd.certificate_number as certificate_number,
            dcd.certificate_issued_date as issue_date,
            dcd.certificate_effective_date as valid_begin_date,
            dcd.certificate_expiring_date as valid_end_date,
            dcd.surface_data as surface_data,
            dcd.certificate_status as status,
            dcd.creator_id as creator,
            dcd.create_time as create_time,
            dcd.operator_id as operator,
            dcd.update_time as update_time,
            dcd.file_path as file_path,
            dcd.sync_status as sync_status,
            dcd.remarks as remarks,
            dcd.dept_id as dept_id,

            -- 证照目录字段
            dcctd.CERT_TYPE_DIR_ID as catalog_id,
            dcctd.CERTIFICATE_TYPE_NAME as catalog_name,
            dcctd.CERTIFICATE_TYPE_NAME as certificate_type,
            dcctd.CERTIFICATE_TYPE_CODE as certificate_type_code,
            dcctd.CERTIFICATE_DEFINE_AUTHORITY_NAME as certificate_define_dept,
            dcctd.CERTIFICATE_DEFINE_AUTHORITY_CODE as certificate_define_dept_code,
            dcctd.RELATED_ITEM_NAME as related_item_name,
            dcctd.RELATED_ITEM_CODE as related_item_code,
            dcctd.CERTIFICATE_HOLDER_CATEGORY as holder_type,
            dcctd.VALIDITY_RANGE as certificate_life,
            dcctd.CREATE_TIME as dir_create_time,
            dcctd.UPDATE_TIME as dir_update_time,
            dcctd.UPDATE_BY as operator_id,
            dcctd.APPROVAL_STATUS as status,
            dcctd.CREATE_ORG_CODE as area_code
        FROM dwdb_certificate_data dcd
        INNER JOIN dwdb_ctf_cert_type_directory dcctd
            ON dcd.certificate_type_code = dcctd.CERTIFICATE_TYPE_CODE
        WHERE dcd.data_id in
        <foreach collection="dataIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        <choose>
            <when test='taskName == "海事船员管理系统电子证照接口"'>
                AND dcctd.parent_id = '3'
                <!-- 引航员船员适任证书不需要推送 -->
                AND dcd.certificate_name != '引航员船员适任证书'
            </when>
            <otherwise>
                AND dcd.certificate_type_name IN
                <foreach item="code" collection="certificateTypeName" open="(" separator="," close=")">
                    #{code}
                </foreach>
            </otherwise>
        </choose>
    </select>

</mapper>
