package com.js.dzzz.vo;

import lombok.Data;

/**
 * 证照数据VO
 */
@Data
public class SrCertDataVO {
    /**
     * 证照数据主键
     */
    private String dataId;

    /**
     * 电子证照标识
     */
    private String certificateId;

    /**
     * 目录ID
     */
    private String catalogId;

    /**
     * 目录名称
     */
    private String catalogName;

    /**
     * 模板ID
     */
    private String templateId;

    /**
     * 证照类型名称
     */
    private String certificateType;

    /**
     * 证照类型代码
     */
    private String certificateTypeCode;

    /**
     * 证照颁发机构
     */
    private String issueDept;

    /**
     * 证照颁发机构代码
     */
    private String issueDeptCode;

    /**
     * 证照所属地区编码
     */
    private String certificateAreaCode;

    /**
     * 持证主体
     */
    private String certificateHolder;

    /**
     * 持证主体代码
     */
    private String certificateHolderCode;

    /**
     * 持证主体代码类型
     */
    private String certificateHolderType;

    /**
     * 证照编号
     */
    private String certificateNumber;

    /**
     * 证照颁发日期
     */
    private String issueDate;

    /**
     * 证照有效期起始日期
     */
    private String validBeginDate;

    /**
     * 证照有效期截止日期
     */
    private String validEndDate;

    /**
     * 照面拓展信息
     */
    private String surfaceData;

    /**
     * 证照状态 0-草稿，1-待签章，2-已签章，-1作废
     */
    private String status;

    /**
     * 登记人员ID
     */
    private String creator;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 最后操作人
     */
    private String operator;

    /**
     * 修改时间
     */
    private String updateTime;

    /**
     * 文件保存位置
     */
    private String filePath;

    /**
     * 同步状态 0-新增未上传,1-已上传，2-更新未上传，3-更新已上传, 4-删除未上传,5-删除未上传
     */
    private String syncStatus;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 登记部门
     */
    private String deptId;
}