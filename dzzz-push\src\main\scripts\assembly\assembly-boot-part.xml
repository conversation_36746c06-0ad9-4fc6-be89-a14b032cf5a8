<?xml version='1.0' encoding='UTF-8'?>
<assembly xmlns="http://maven.apache.org/ASSEMBLY/2.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/ASSEMBLY/2.0.0 http://maven.apache.org/xsd/assembly-2.0.0.xsd">

	<id>boot</id>

	<!-- <formats> 支持 zip，tar，tar.gz 等 <format>${assemblyFormat}</format> </formats> -->

	<formats>
		<format>zip</format>
	</formats>

	<includeBaseDirectory>true</includeBaseDirectory>
	<baseDirectory>${project.artifactId}-${project.version}</baseDirectory>
	<fileSets>
		<!--把项目自己编译出来的jar文件,打包进zip文件的根目录 -->
		<fileSet>
			<directory>target</directory>
			<outputDirectory>/</outputDirectory>
			<includes>
				<include>*.jar</include>
			</includes>
		</fileSet>
		<fileSet>
			<directory>src/main/resources</directory> <!-- 指定归档文件（要打的jar包）要包含的目录（下的文件及文件夹） -->
			<outputDirectory>config</outputDirectory> <!-- 指定要将当前目录（<directory>标签中的目录放在归档文件（要打的jar包）bin目录下） -->
			<includes>
				<include>*.yml</include>  <!-- 精确控制要包含的文件，<exclude>用于精确控制要排除的文件 -->
				<include>*.yaml</include>
				<include>*logback.xml</include>
				<include>*.properties</include>
			</includes>
		</fileSet>
		 
		<fileSet>
			<directory>src/main/scripts</directory>
			<outputDirectory>/</outputDirectory>
			<includes>
				<include>*.sh</include>
				<include>*.bat</include>
			</includes>
		</fileSet>
	</fileSets>

</assembly>