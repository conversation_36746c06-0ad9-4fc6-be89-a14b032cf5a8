package com.js.dzzz.entity;

import com.baomidou.mybatisplus.annotation.IdType;import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 国家平台证照推送表
 */
@Data
@TableName("dwdb_ctf_cert_national_push")
public class DwdbCtfCertNationalPush {

    @TableId(type = IdType.ASSIGN_UUID)
    private String certPushId;

    /**
     * 证照记录data_id
     */
    private String dataId;

    /**
     * 证照id
     */
    private String certificateId;

    /**
     * 证照编号
     */
    private String certificateNumber;

    /**
     * 证照名称
     */
    private String certificateName;

    /**
     * 证照类型名称
     */
    private String certificateTypeCode;

    /**
     * 证照类型名称
     */
    private String certificateTypeName;

    /**
     * 证照状态，1-推送成功 2-推送失败
     */
    private String pushStatus;

    /**
     * 推送次数
     */
    private int pushNumber;

    /**
     * 推送详情
     */
    private String pushDesc;

    /**
     * 推送时间
     */
    private Date pushDate;
}
