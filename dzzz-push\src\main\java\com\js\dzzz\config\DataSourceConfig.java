package com.js.dzzz.config;

import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.sql.DataSource;

@Configuration
@MapperScan(basePackages = "com.js.dzzz.mapper", sqlSessionFactoryRef = "standardSqlSessionFactory")
public class DataSourceConfig {

    @Primary
    @Bean(name = "standardDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.standard")
    public DataSource standardDataSource() {
        return new HikariDataSource();
    }

    @Bean(name = "standardSqlSessionFactory")
    public SqlSessionFactory standardSqlSessionFactory(@Qualifier("standardDataSource") DataSource dataSource)
            throws Exception {
        MybatisSqlSessionFactoryBean sqlSessionFactory = new MybatisSqlSessionFactoryBean();
        sqlSessionFactory.setDataSource(dataSource);
        PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        Resource[] resources = resolver.getResources("classpath*:mapper/*.xml");
        if (resources != null && resources.length > 0) {
            sqlSessionFactory.setMapperLocations(resources);
        }
        return sqlSessionFactory.getObject();
    }
}