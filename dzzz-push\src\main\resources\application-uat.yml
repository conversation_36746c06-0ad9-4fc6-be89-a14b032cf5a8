spring:
  datasource:
    # 标准库数据源配置
    standard:
      jdbc-url: ***********************************************************************************************************************************
      username: dzzz2
      password: Co25@Msa*0415
      driver-class-name: org.postgresql.Driver
      # 连接池优化配置
      maximum-pool-size: 10               # 连接池最大连接数
      minimum-idle: 5                     # 连接池最小空闲连接数
      idle-timeout: 60000                 # 空闲连接超时时间(毫秒)
      connection-timeout: 30000           # 连接超时时间(毫秒)
      max-lifetime: 1800000               # 连接最大生命周期(毫秒，30分钟)
      auto-commit: true                   # 自动提交
      pool-name: StandardHikariCP         # 连接池名称
      validation-timeout: 5000            # 验证连接的最大等待时间(毫秒)
      connection-test-query: SELECT 1     # 连接测试查询
      leak-detection-threshold: 60000     # 连接泄露检测阈值(毫秒)

logging:
  level:
    com.js.dzzz: debug

job:
  shipCertPushJob:
    # 是否启用
    enabled: false
    # 推送地址
    pushUrl: http://***********:28080/v1/service/cert/sr/demo
    # 认证用户
    authUser: tcauthsr
    # 认证密码
    authPassword: sr0041
    # 定时任务执行时间
    cron: "0/10 * * * * ?"
    # 单批推送数量
    batchSize: 500
  shipCheckCertPushJob:
    # 是否启用
    enabled: false
    # 推送地址
    pushUrl: http://***********:28080/v1/service/cert/sv/demo
    # 认证用户
    authUser: tcauthsv
    # 认证密码
    authPassword: sv0041
    # 定时任务执行时间
    cron: "0/10 * * * * ?"
    # 单批推送数量
    batchSize: 500
  shipCrewCertPushJob:
    # 是否启用
    enabled: false
    # 推送地址
    pushUrl: http://***********:28080/v1/service/cert/crw/demo
    # 认证用户
    authUser: tcauthcrw
    # 认证密码
    authPassword: crw0041
    # 定时任务执行时间
    cron: "0/10 * * * * ?"
    # 单批推送数量
    batchSize: 500