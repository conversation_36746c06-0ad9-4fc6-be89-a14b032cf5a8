package com.js.dzzz.job;

import com.js.dzzz.enums.CertPushTaskEnum;
import com.js.dzzz.service.CertificatePushService;
import com.js.dzzz.vo.CertPushRequestVO;
import com.js.dzzz.vo.CertificatePushVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 船员证书推送任务（船员业务）
 */
@Slf4j
@Component
public class ShipCrewCertPushJob extends PushJob{

    @Resource
    private CertificatePushService certificatePushService;
    // 船员类型代码
    private static final List<String> CERTIFICATE_CODES = Arrays.asList(
            "0111", "0120", "0125", "0108", "0116",
            "90103", "0107", "0106", "0105", "90102",
            "0113", "0132", "0109", "0104", "0112",
            "0110", "0133", "0134", "0118", "0135",
            "0119", "0140", "0117");

    @Value("${job.shipCrewCertPushJob.enabled}")
    private boolean enable;

    @Value("${job.shipCrewCertPushJob.pushUrl}")
    private String pushUrl;

    @Value("${job.shipCrewCertPushJob.authUser}")
    private String authUser;

    @Value("${job.shipCrewCertPushJob.authPassword}")
    private String authPassword;

    @Value("${job.shipCrewCertPushJob.batchSize:100}")
    private int batchSize;

    @Scheduled(cron = "${job.shipCrewCertPushJob.cron}")
    @Transactional(rollbackFor = Exception.class)
    public void execute() {
        if (!enable) {
            return;
        }

        String name = CertPushTaskEnum.SHIP_CREW_CERT.getName();
        Date start = new Date();
        log.info("开始执行{}定时任务:{}", name, start);

        try {
            this.push(name);
        } catch (Exception e) {
            log.error("{}执行异常", name, e);
        }
        Date end = new Date();
        log.info("{}定时任务结束:{}，耗时:{}", name, end, end.getTime() - start.getTime());
    }

    @Override
    public void push(String name) {
        // 封装请求参数
        CertPushRequestVO requestParams = CertPushRequestVO.builder()
                .name(name)
                .url(pushUrl)
                .authUser(authUser)
                .authPassword(authPassword)
                .build();

        // 先重试推送失败的数据，重试间隔5分钟
        certificatePushService.retryFailedPushData(name, requestParams, 5, CERTIFICATE_CODES);

        // 查询需要推送的证照数据
        List<CertificatePushVO> pushVOList = certificatePushService
                .queryCertificateDataByType(name, CERTIFICATE_CODES, batchSize);
        if (pushVOList.isEmpty()) {
            log.info("{}没有需要推送的数据", name);
            return;
        }

        log.info("{}查询到{}条需要推送的数据", name, pushVOList.size());

        // 推送数据
        boolean success = certificatePushService.pushCertificateData(pushVOList, requestParams);
        log.info("{}推送完成，结果：{}", name, success ? "成功" : "失败");
    }
}
