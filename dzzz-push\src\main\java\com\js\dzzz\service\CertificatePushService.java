package com.js.dzzz.service;

import cn.hutool.json.JSONObject;
import com.js.dzzz.vo.CertPushRequestVO;
import com.js.dzzz.vo.CertificatePushVO;
import com.js.dzzz.vo.SrCertCatalogVO;
import com.js.dzzz.vo.SrCertDataVO;

import java.util.List;

/**
 * 证照推送服务
 */
public interface CertificatePushService {

    /**
     * 根据证照类型代码查询证照数据和目录数据
     *
     * @param taskName            任务名称
     * @param certificateTypeCode 证照类型代码
     * @return 证照数据和目录数据组合列表
     */
    List<CertificatePushVO> queryCertificateDataByType(String taskName, List<String> certificateTypeCode, int pushCount);

    /**
     * 推送证照数据到国家平台
     *
     * @param pushVOList    证照数据和目录数据组合列表
     * @param requestParams 推送请求参数
     * @return 是否推送成功
     */
    boolean pushCertificateData(List<CertificatePushVO> pushVOList, CertPushRequestVO requestParams);

    /**
     * 保存或更新推送记录，统一处理保存推送结果和更新重试状态的逻辑
     * @param jsonObject  包含证照数据的JSON对象
     * @param pushStatus  推送状态，1-成功，2-失败，3-失败且不再重试
     * @param pushDesc    推送描述
     */
    void savePushResult(JSONObject jsonObject, String pushStatus, String pushDesc);

    /**
     * 重试推送失败的数据
     * 
     * @param taskName             任务名称
     * @param requestParams        推送请求参数
     * @param retryIntervalMinutes 距离上次推送的最小间隔（分钟）
     * @param certificateTypeNames 证照类型名称列表
     */
    void retryFailedPushData(String taskName, CertPushRequestVO requestParams, int retryIntervalMinutes,
            List<String> certificateTypeNames);
}
