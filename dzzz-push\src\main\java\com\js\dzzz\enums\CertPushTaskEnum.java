package com.js.dzzz.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 证照推送任务枚举
 */
@Getter
@AllArgsConstructor
public enum CertPushTaskEnum {

    SHIP_CERT("shipCert", "船舶登记系统电子证照接口", "船舶国籍证书"),
    SHIP_CHECK_CERT("shipCheckCert", "船舶检验系统电子证照接口", "油污残骸"),
    SHIP_CREW_CERT("shipCrewCert", "海事船员管理系统电子证照接口", "船员业务");

    /**
     * 推送任务编码
     */
    private final String code;

    /**
     * 推送任务名称
     */
    private final String name;

    /**
     * 证照类型描述
     */
    private final String desc;

    public static CertPushTaskEnum getCertPushTaskByCode(String code) {
        for (CertPushTaskEnum certPushTask : values()) {
            if (certPushTask.getCode().equals(code)) {
                return certPushTask;
            }
        }
        return null;
    }
}