package com.js.dzzz.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("dwdb_certificate_data")
//标准库电子证照信息表
public class DwdbCertificateData {
    //证照数据主键
    @TableId
    private String dataId;
    
    //电子证照业务标识码
    private String certificateId;
    
    //证照模板ID
    private String templateId;
    
    //证照类型名称
    private String certificateTypeName;
    
    //证照类型代码，21位编码
    private String certificateTypeCode;
    
    //证照定义机构
    private String certificateDefineAuthorityName;
    
    //证照定义机构代码（统一社会信用代码）
    private String certificateDefineAuthorityCode;
    
    //关联事项名称
    private String relatedItemName;
    
    //关联事项编码
    private String relatedItemCode;
    
    //持证主体类别：1-自然人 2-法人或其他组织 3-混合 4-其他
    private String certificateHolderCategory;
    
    //持证主体类别名称：自然人/法人或其他组织/混合/其他
    private String certificateHolderCategoryName;
    
    //有效期限范围，多个用^分隔不同期限
    private String validityRange;
    
    //电子证照唯一标识码
    private String certificateIdentifier;
    
    //证照名称
    private String certificateName;
    
    //证照编号
    private String certificateNumber;
    
    //证照颁发机构
    private String certificateIssuingAuthorityName;
    
    //证照颁发机构代码（统一社会信用代码）
    private String certificateIssuingAuthorityCode;
    
    //证照颁发日期（yyyy-mm-dd）
    private String certificateIssuedDate;
    
    //持证主体名称（自然人姓名/法人全称）
    private String certificateHolderName;
    
    //持证主体代码（信用代码/身份证号等）
    private String certificateHolderCode;
    
    //持证主体代码类型：统一社会信用代码/公民身份号码/护照号/其他
    private String certificateHolderTypeName;
    
    //证照有效期开始日期（yyyy-mm-dd）
    private String certificateEffectiveDate;
    
    //证照有效期截止日期（yyyy-mm-dd或长期）
    private String certificateExpiringDate;
    
    //证照颁发机构代码（海事内部统一编码2.0版本）
    private String issueDeptCode2;
    
    //证照颁发机构代码（海事内部统一编码3.0版本）
    private String issueDeptCode3;
    
    //证照所属地区编码
    private String certificateAreaCode;
    
    //照面拓展信息
    private String surfaceData;
    
    //证照状态（-5 异常,-4 撤回,-3 撤销,-2 注销,-1 作废,0 首次生成,1 生成已同步,2 修改未同步,3 过期,4 修改已同步,5 预览）
    private String certificateStatus;
    
    //登记人员ID
    private String creatorId;
    
    //创建时间
    private String createTime;
    
    //最后操作人
    private String operatorId;
    
    //修改时间
    private String updateTime;
    
    //文件保存位置
    private String filePath;
    
    //同步状态（0-新增未上传,1-已上传，2-更新未上传，3-更新已上传,4-删除未上传,5-删除未上传）
    private String syncStatus;
    
    //备注
    private String remarks;
    
    //登记部门
    private String deptId;
    
    //申请编号
    private String applyNum;
    
    //事项类型
    private String affairType;
    
    //服务对象
    private String serveBusiness;
    
    //事项ID
    private String affairId;
    
    //事项编号
    private String affairNum;
    
    //签章类型
    private String qzType;
    
    //加签文件备用地址
    private String draftUrl;
    
    //归档编号
    private String sortName;
    
    //印章名称
    private String sealname;
    
    //源系统代码
    private String sourceCode;
    
    //记录创建日期
    private Date recCreateDate;
    
    //记录修改日期
    private Date recModifyDate;
    
    //数据归属机构代码
    private String msaOrgCode;
    
    //出生年月日-yyyymmdd
    private String birth;
    
    //持证人姓名-英文
    private String nameEn;
    
    //国籍-中文
    private String countryCn;
    
    //国籍-英文
    private String countryEn;
    
    //发证机关(英文）
    private String signDeptEn;
    
    //适用航线-中文
    private String applivationsCn;
    
    //船员类型
    private String crewType;

    //游艇驾驶证资格
    private String qualificationCn;

    //出生日期-英文
    private String birthEn;

    //证书印刷号
    private String certPrintNo;

    //证照有效期开始日期（英文）
    private String certificateEffectiveDateEn;
    
    //证照有效期截止日期（英文）
    private String certificateExpiringDateEn;
    
    //证照颁发日期（英文）
    private String certificateIssuedDateEn;
    
    //职务(英文)
    private String crewTypeEn;
    
    //适用航线-英文
    private String applivationsEn;
    
    //授权机关（中文）
    private String authAuthorityCn;
    
    //授权机关（英文）
    private String authAuthorityEn;
    
    //审核机构（中文）
    private String evaOrgCn;
    
    //审核机构（英文）
    private String evaOrgEn;
    
    //培训主管姓名（中文）
    private String trainManagerNameCn;
    
    //培训主管姓名（英文）
    private String trainManagerNameEn;
    
    //法定代表人姓名（中文）
    private String representativeCn;
    
    //法定代表人姓名（英文）
    private String representativeEn;
    
    //培训项目（中文）
    private String trainingNamesCn;
    
    //培训项目（英文）
    private String trainingNamesEn;
    
    //培训项目签发日期（中文）多个项目用#隔开
    private String trainingIssueDatesCn;
    
    //培训项目签发日期（英文）多个项目用#隔开
    private String trainingIssueDatesEn;
    
    //培训项目有效期至（中文）多个项目用#隔开
    private String trainingEffectiveDatesCn;
    
    //培训项目有效期至（英文）多个项目用#隔开    
    private String trainingEffectiveDatesEn;
    
    //机构代码
    private String trainingInstitutionCode;
    
    //培训地点
    private String trainingLocation;
} 