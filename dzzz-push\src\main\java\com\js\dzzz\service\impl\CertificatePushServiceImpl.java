package com.js.dzzz.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.js.dzzz.constant.CertificateTypeMapping;
import com.js.dzzz.entity.DwdbCtfCertNationalPush;
import com.js.dzzz.mapper.DwdbCertificateDataMapper;
import com.js.dzzz.service.CertificatePushService;
import com.js.dzzz.service.DwdbCtfCertNationalPushService;
import com.js.dzzz.vo.CertPushRequestVO;
import com.js.dzzz.vo.CertificatePushVO;
import com.js.dzzz.vo.SrCertCatalogVO;
import com.js.dzzz.vo.SrCertDataVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 证照推送服务实现类
 */
@Slf4j
@Service
public class CertificatePushServiceImpl implements CertificatePushService {

    /**
     * 推送状态：成功
     */
    private static final String PUSH_STATUS_SUCCESS = "1";

    /**
     * 推送状态：失败
     */
    private static final String PUSH_STATUS_FAIL = "2";

    /**
     * 推送成功描述
     */
    private static final String PUSH_DESC_SUCCESS = "推送成功";

    @Resource
    private DwdbCertificateDataMapper certificateDataMapper;

    @Resource
    private DwdbCtfCertNationalPushService dwdbCtfCertNationalPushService;

    @Override
    public List<CertificatePushVO> queryCertificateDataByType(String taskName, List<String> certificateTypeName,int pushCount) {
        log.info("[{}]开始查询证照数据", taskName);

        // 关联查询证照数据和证照类型目录
        List<Map<String, Object>> resultList = certificateDataMapper.selectCertificateDataByType(certificateTypeName,
                taskName, pushCount);
        if (CollUtil.isEmpty(resultList)) {
            log.warn("[{}]未查询到证照数据", taskName);
            return new ArrayList<>();
        }

        // 转换为VO对象
        List<CertificatePushVO> pushVOList = resultList.stream()
                .map(this::convertToPushVO)
                .filter(ObjectUtil::isNotEmpty)
                .collect(Collectors.toList());

        log.info("[{}]查询到证照类型[{}]的证照数据{}条", taskName, certificateTypeName, pushVOList.size());
        return pushVOList;
    }

    /**
     * 将Map结果转换为推送VO对象
     *
     * @param result 查询结果Map
     * @return 推送VO对象
     */
    private CertificatePushVO convertToPushVO(Map<String, Object> result) {
        CertificatePushVO pushVO = new CertificatePushVO();
        pushVO.setCertData(convertToCertDataVO(result));
        pushVO.setCertCatalog(convertToCertCatalogVO(result));
        if (pushVO.getCertData() == null || pushVO.getCertCatalog() == null) {
            log.warn("[{}]证照数据VO转换失败", pushVO);
            return null;
        }
        return pushVO;
    }

    /**
     * 转换为证照数据VO
     *
     * @param result 查询结果Map
     * @return 证照数据VO
     */
    private SrCertDataVO convertToCertDataVO(Map<String, Object> result) {
        // 证照类型 映射
        String certificateType = getString(result, "certificate_type");
        String certificateTypeCode = CertificateTypeMapping.CERTIFICATE_TYPE_TO_CODE
                .get(certificateType);
        if (StrUtil.isBlank(certificateTypeCode)) {
            log.warn("证照数据VO[{}]未找到映射关系", certificateType);
            return null;
        }
        SrCertDataVO certDataVO = new SrCertDataVO();
        // 基本信息
        certDataVO.setDataId(getString(result, "data_id"));
        certDataVO.setCertificateId(getString(result, "certificate_id"));
        certDataVO.setCatalogId(getString(result, "catalog_id"));
        certDataVO.setCatalogName(getString(result, "catalog_name"));
        certDataVO.setTemplateId(getString(result, "template_id"));
        certDataVO.setCertificateType(certificateType);
        certDataVO.setCertificateTypeCode(certificateTypeCode);

        // 机构信息
        certDataVO.setIssueDept(getString(result, "issue_dept"));
        certDataVO.setIssueDeptCode(getString(result, "issue_dept_code"));
        certDataVO.setCertificateAreaCode(getString(result, "certificate_area_code"));

        // 持证主体信息
        certDataVO.setCertificateHolder(getString(result, "certificate_holder"));
        certDataVO.setCertificateHolderCode(getString(result, "certificate_holder_code"));
        certDataVO.setCertificateHolderType(getString(result, "certificate_holder_type"));
        certDataVO.setCertificateNumber(getString(result, "certificate_number"));

        // 日期信息
        certDataVO.setIssueDate(getString(result, "issue_date"));
        certDataVO.setValidBeginDate(getString(result, "valid_begin_date"));
        certDataVO.setValidEndDate(getString(result, "valid_end_date"));

        // 其他信息
        certDataVO.setSurfaceData(getString(result, "surface_data"));
        certDataVO.setStatus(getString(result, "status"));
        certDataVO.setCreator(getString(result, "creator"));
        certDataVO.setCreateTime(getString(result, "create_time"));
        certDataVO.setOperator(getString(result, "operator"));
        certDataVO.setUpdateTime(getString(result, "update_time"));
        certDataVO.setFilePath(getString(result, "file_path"));
        certDataVO.setSyncStatus(getString(result, "sync_status"));
        certDataVO.setRemarks(getString(result, "remarks"));
        certDataVO.setDeptId(getString(result, "dept_id"));

        return certDataVO;
    }

    /**
     * 转换为证照目录VO
     *
     * @param result 查询结果Map
     * @return 证照目录VO
     */
    private SrCertCatalogVO convertToCertCatalogVO(Map<String, Object> result) {
        // 证照类型 映射
        String certificateType = getString(result, "certificate_type");
        String certificateTypeCode = CertificateTypeMapping.CERTIFICATE_TYPE_TO_CODE
                .get(certificateType);
        if (StrUtil.isBlank(certificateTypeCode)) {
            log.warn("证照目录VO[{}]未找到映射关系", certificateType);
            return null;
        }
        SrCertCatalogVO certCatalogVO = new SrCertCatalogVO();
        // 基本信息
        certCatalogVO.setCatalogId(getString(result, "catalog_id"));
        certCatalogVO.setCatalogName(getString(result, "catalog_name"));
        certCatalogVO.setCertificateType(certificateType);
        certCatalogVO.setCertificateTypeCode(certificateTypeCode);

        // 证照定义机构信息 按固定值处理
        certCatalogVO.setCertificateDefineDept("中华人民共和国交通运输部");
        certCatalogVO.setCertificateDefineDeptCode("11100000000019713D");

        // 关联信息
        certCatalogVO.setRelatedItemName(getString(result, "related_item_name"));
        certCatalogVO.setRelatedItemCode(getString(result, "related_item_code"));
        certCatalogVO.setHolderType(getString(result, "holder_type"));
        certCatalogVO.setCertificateLife(getString(result, "certificate_life"));

        // 其他信息
        certCatalogVO.setCreateTime(getString(result, "dir_create_time"));
        certCatalogVO.setUpdateTime(getString(result, "dir_update_time"));
        certCatalogVO.setOperatorId(getString(result, "operator_id"));
        certCatalogVO.setStatus(getString(result, "status"));
        certCatalogVO.setAreaCode(getString(result, "area_code"));

        return certCatalogVO;
    }

    @Override
    public boolean pushCertificateData(List<CertificatePushVO> pushVOList, CertPushRequestVO requestParams) {
        String name = requestParams.getName();
        if (StrUtil.isBlank(name)) {
            log.warn("推送参数为空");
            return false;
        }

        if (CollUtil.isEmpty(pushVOList)) {
            log.warn("[{}]证照数据列表为空，无需推送", name);
            return false;
        }

        log.info("[{}]开始推送证照数据，数据量：{}", name, pushVOList.size());
        // 构建请求参数并设置到requestParams中
        JSONObject jsonObject = buildRequestJson(pushVOList);
        requestParams.setParam(jsonObject);

        log.info("[{}]推送证照数据请求参数：{}", name, JSONUtil.toJsonStr(jsonObject));
        // 调用推送接口并处理结果
        boolean success = doPushCertificateData(requestParams);
        log.info("[{}]证照数据推送完成，结果：{}", name, success ? "成功" : "失败");
        return success;
    }

    /**
     * 构建推送请求JSON，首字母大写
     */
    private JSONObject buildRequestJson(List<CertificatePushVO> pushVOList) {
        List<JSONObject> certDataJsonList = new ArrayList<>();
        List<JSONObject> catalogJsonList = new ArrayList<>();

        for (CertificatePushVO pushVO : pushVOList) {
            // 处理证照数据
            JSONObject certDataJson = new JSONObject();
            SrCertDataVO certData = pushVO.getCertData();
            certDataJson.set("DataId", certData.getDataId());
            certDataJson.set("CertificateId", certData.getCertificateId());
            certDataJson.set("CatalogId", certData.getCatalogId());
            certDataJson.set("CatalogName", certData.getCatalogName());
            certDataJson.set("TemplateId", certData.getTemplateId());
            certDataJson.set("CertificateType", certData.getCertificateType());
            certDataJson.set("CertificateTypeCode", certData.getCertificateTypeCode());
            certDataJson.set("IssueDept", certData.getIssueDept());
            certDataJson.set("IssueDeptCode", certData.getIssueDeptCode());
            certDataJson.set("CertificateAreaCode", certData.getCertificateAreaCode());
            certDataJson.set("CertificateHolder", certData.getCertificateHolder());
            certDataJson.set("CertificateHolderCode", certData.getCertificateHolderCode());
            certDataJson.set("CertificateHolderType", certData.getCertificateHolderType());
            certDataJson.set("CertificateNumber", certData.getCertificateNumber());
            certDataJson.set("IssueDate", certData.getIssueDate());
            certDataJson.set("ValidBeginDate", certData.getValidBeginDate());
            certDataJson.set("ValidEndDate", certData.getValidEndDate());
            certDataJson.set("SurfaceData", certData.getSurfaceData());
            certDataJson.set("Status", certData.getStatus());
            certDataJson.set("Creator", certData.getCreator());
            certDataJson.set("CreateTime", certData.getCreateTime());
            certDataJson.set("Operator", certData.getOperator());
            certDataJson.set("UpdateTime", certData.getUpdateTime());
            certDataJson.set("FilePath", certData.getFilePath());
            certDataJson.set("SyncStatus", certData.getSyncStatus());
            certDataJson.set("Remarks", certData.getRemarks());
            certDataJson.set("DeptId", certData.getDeptId());
            certDataJsonList.add(certDataJson);

            // 处理证照目录
            JSONObject catalogJson = new JSONObject();
            SrCertCatalogVO catalog = pushVO.getCertCatalog();
            catalogJson.set("CatalogId", catalog.getCatalogId());
            catalogJson.set("CatalogName", catalog.getCatalogName());
            catalogJson.set("CertificateType", catalog.getCertificateType());
            catalogJson.set("CertificateTypeCode", catalog.getCertificateTypeCode());
            catalogJson.set("CertificateDefineDept", catalog.getCertificateDefineDept());
            catalogJson.set("CertificateDefineDeptCode", catalog.getCertificateDefineDeptCode());
            catalogJson.set("RelatedItemName", catalog.getRelatedItemName());
            catalogJson.set("RelatedItemCode", catalog.getRelatedItemCode());
            catalogJson.set("HolderType", catalog.getHolderType());
            catalogJson.set("CertificateLife", catalog.getCertificateLife());
            catalogJson.set("CreateTime", catalog.getCreateTime());
            catalogJson.set("UpdateTime", catalog.getUpdateTime());
            catalogJson.set("OperatorId", catalog.getOperatorId());
            catalogJson.set("Status", catalog.getStatus());
            catalogJson.set("AreaCode", catalog.getAreaCode());
            catalogJson.set("Remarks", catalog.getRemarks());
            catalogJsonList.add(catalogJson);
        }

        // 组合数据
        JSONObject pushJson = new JSONObject();
        pushJson.set("sv_cert_data", certDataJsonList);
        pushJson.set("sv_cert_catalog", catalogJsonList);
        pushJson.set("operate", "add");
        return pushJson;
    }

    /**
     * 执行推送操作
     *
     * @param requestParams 推送请求参数
     * @return 是否推送成功
     */
    private boolean doPushCertificateData(CertPushRequestVO requestParams) {
        try {
            String taskName = requestParams.getName();
            JSONObject jsonObject = requestParams.getParam();

            String authHeader = "Basic "
                    + Base64.encode(requestParams.getAuthUser() + ":" + requestParams.getAuthPassword());

            log.info("[{}]推送请求URL：{}", taskName, requestParams.getUrl());

            // 执行HTTP请求
            HttpResponse response = HttpUtil.createPost(requestParams.getUrl())
                    .header("Content-Type", "application/json")
                    .header("Authorization", authHeader)
                    .body(jsonObject.toString())
                    .execute();

            if (response == null) {
                log.error("[{}]推送失败，HTTP请求失败", taskName);
                savePushResult(jsonObject, PUSH_STATUS_FAIL, "请求无响应内容");
                return false;
            }
            // 获取响应内容
            String responseBody = response.body();
            log.info("[{}]推送响应状态码：{}，响应内容：{}", taskName, response.getStatus(), responseBody);
            // 判断HTTP请求是否成功
            if (!response.isOk()) {
                String errorMsg = "HTTP状态码：" + response.getStatus() + "，响应内容：" + responseBody;
                log.error("[{}]推送失败，{}", taskName, errorMsg);
                savePushResult(jsonObject, PUSH_STATUS_FAIL, errorMsg);
                return false;
            }
            // 解析响应JSON
            JSONObject respJson = JSONUtil.parseObj(responseBody);
            int code = respJson.getInt("code", -1);
            // 判断响应码是否为200
            if (code != 200) {
                String msg = respJson.getStr("msg", "未知错误");
                log.error("[{}]推送失败，响应码：{}，错误信息：{}", taskName, code, msg);
                savePushResult(jsonObject, PUSH_STATUS_FAIL, msg);
                return false;
            }
            log.info("[{}]推送成功，响应码：{}", taskName, code);
            savePushResult(jsonObject, PUSH_STATUS_SUCCESS, PUSH_DESC_SUCCESS);
            return true;
        } catch (Exception e) {
            // 处理所有异常，包括HTTP请求异常和JSON解析异常
            String errorMsg = "推送请求异常：" + e.getMessage();
            log.error("[{}]{}", requestParams.getName(), errorMsg, e);
            savePushResult(requestParams.getParam(), PUSH_STATUS_FAIL, errorMsg);
            return false;
        }
    }

    /**
     * 保存或更新推送记录，统一处理保存推送结果和更新重试状态的逻辑
     * @param jsonObject  包含证照数据的JSON对象
     * @param pushStatus  推送状态，1-成功，2-失败，3-失败且不再重试
     * @param pushDesc    推送描述
     */
    @Override
    public void savePushResult(JSONObject jsonObject, String pushStatus, String pushDesc) {
        log.info("保存证照推送结果，状态：{}，描述：{}", pushStatus, pushDesc);
        List<JSONObject> certDataList = jsonObject.getBeanList("sv_cert_data", JSONObject.class);
        if (CollUtil.isEmpty(certDataList)) {
            log.warn("无证照数据需要保存推送结果");
            return;
        }

        // 提取所有dataId
        List<String> dataIdList = certDataList.stream()
                .map(json -> json.getStr("DataId"))
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(dataIdList)) {
            log.warn("无有效的dataId需要保存推送结果");
            return;
        }

        // 批量查询已存在的记录
        LambdaQueryWrapper<DwdbCtfCertNationalPush> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(DwdbCtfCertNationalPush::getDataId, dataIdList);
        List<DwdbCtfCertNationalPush> existRecords = dwdbCtfCertNationalPushService.list(queryWrapper);

        // 转换为Map，方便快速查找
        Map<String, DwdbCtfCertNationalPush> existRecordMap = existRecords.stream()
                .collect(Collectors.toMap(
                        DwdbCtfCertNationalPush::getDataId,
                        record -> record,
                        (o1, o2) -> o1));

        // 批量处理需要更新和新增的记录
        List<DwdbCtfCertNationalPush> updateList = new ArrayList<>();
        List<DwdbCtfCertNationalPush> insertList = new ArrayList<>();

        // 当前时间
        Date now = DateUtil.date();

        for (JSONObject cert : certDataList) {
            String dataId = cert.getStr("DataId");
            // 判断是更新还是插入
            if (existRecordMap.containsKey(dataId)) {
                // 更新现有记录
                DwdbCtfCertNationalPush record = existRecordMap.get(dataId);
                // 计算pushNumber
                int pushNumber = record.getPushNumber() + 1;
                if (pushNumber >= 3 && PUSH_STATUS_FAIL.equals(pushStatus)) {
                    pushStatus = "3"; // 设置为不再重试状态
                    log.error("data_id={}推送失败已超过3次，停止重试！错误信息:{}", dataId, pushDesc);
                }
                record.setPushStatus(pushStatus);
                record.setPushDesc(pushDesc);
                record.setPushDate(now);
                record.setPushNumber(pushNumber);
                updateList.add(record);
                continue;
            }
            // 创建新记录
            DwdbCtfCertNationalPush newRecord = new DwdbCtfCertNationalPush();
            newRecord.setDataId(dataId);
            newRecord.setCertificateId(cert.getStr("CertificateId"));
            newRecord.setCertificateNumber(cert.getStr("CertificateNumber"));
            newRecord.setCertificateName(cert.getStr("CatalogName"));
            newRecord.setCertificateTypeCode(cert.getStr("CertificateTypeCode"));
            newRecord.setCertificateTypeName(cert.getStr("CertificateType"));
            newRecord.setPushStatus(pushStatus);
            newRecord.setPushDesc(pushDesc);
            newRecord.setPushDate(now);
            newRecord.setPushNumber(1);
            insertList.add(newRecord);
        }

        // 批量更新和插入
        if (CollUtil.isNotEmpty(updateList)) {
            boolean updateResult = dwdbCtfCertNationalPushService.updateBatchById(updateList);
            log.info("批量更新推送结果：{}条记录，结果：{}", updateList.size(), updateResult);
        }

        if (CollUtil.isNotEmpty(insertList)) {
            boolean insertResult = dwdbCtfCertNationalPushService.saveBatch(insertList);
            log.info("批量插入推送结果：{}条记录，结果：{}", insertList.size(), insertResult);
        }
        log.info("保存推送结果完成");

    }

    /**
     * 从Map中获取字符串值
     */
    private String getString(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : null;
    }

    @Override
    public void retryFailedPushData(String taskName, CertPushRequestVO requestParams, int retryIntervalMinutes,
            List<String> certificateTypeNames) {
        log.info("[{}]开始重试推送失败的数据...", taskName);
        // 查询失败且未超过3次，且距离上次推送超过retryIntervalMinutes的数据
        QueryWrapper<DwdbCtfCertNationalPush> query = new QueryWrapper<>();
        query.eq("push_status", PUSH_STATUS_FAIL)
                .lt("push_number", 3)
                .lt("push_date", DateUtil.offsetMinute(DateUtil.date(), -retryIntervalMinutes))
                .orderByAsc("push_date")
                .last("limit 500");
        List<DwdbCtfCertNationalPush> failedList = dwdbCtfCertNationalPushService.list(query);
        if (CollUtil.isEmpty(failedList)) {
            log.info("[{}]无需要重试的失败数据", taskName);
            return;
        }
        List<String> dataIdList = failedList.stream()
                .map(DwdbCtfCertNationalPush::getDataId)
                .collect(Collectors.toList());
        log.info("[{}]本次批量重推dataId列表：{}", taskName, dataIdList);
        // 批量查出完整VO
        List<Map<String, Object>> resultList = certificateDataMapper.selectCertificateDataByDataIds(dataIdList,
                taskName, certificateTypeNames);
        if (CollUtil.isEmpty(resultList)) {
            log.warn("[{}]批量重推时未查到任何证照数据，跳过", taskName);
            return;
        }
        List<CertificatePushVO> pushVOList = resultList.stream()
                .map(this::convertToPushVO)
                .filter(ObjectUtil::isNotEmpty)
                .collect(Collectors.toList());
        log.info("[{}]批量重推证照数量：{}", taskName, pushVOList.size());
        pushCertificateData(pushVOList, requestParams);
        log.info("[{}]重试推送失败数据完成", taskName);
    }

}
